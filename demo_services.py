#!/usr/bin/env python3
"""
Демонстрационный скрипт для добавления примеров услуг
"""

import requests
import json
import time

# Базовый URL API
BASE_URL = "http://localhost:8000/api"

# Демонстрационные услуги
DEMO_SERVICES = [
    {
        "name": "Разработка веб-сайта",
        "description": "Создание современного адаптивного веб-сайта с использованием последних технологий",
        "price": 50000.0,
        "category": "IT услуги"
    },
    {
        "name": "SEO оптимизация",
        "description": "Комплексная поисковая оптимизация сайта для улучшения позиций в поисковых системах",
        "price": 15000.0,
        "category": "Маркетинг"
    },
    {
        "name": "Логотип и фирменный стиль",
        "description": "Разработка уникального логотипа и создание фирменного стиля компании",
        "price": 25000.0,
        "category": "Дизайн"
    },
    {
        "name": "Консультация по бизнесу",
        "description": "Индивидуальная консультация по развитию бизнеса и стратегическому планированию",
        "price": 3000.0,
        "category": "Консультации"
    },
    {
        "name": "Настройка сервера",
        "description": "Установка и настройка веб-сервера, базы данных и системы мониторинга",
        "price": 8000.0,
        "category": "IT услуги"
    },
    {
        "name": "Контент-маркетинг",
        "description": "Создание и продвижение контента в социальных сетях и блогах",
        "price": 12000.0,
        "category": "Маркетинг"
    },
    {
        "name": "Мобильное приложение",
        "description": "Разработка нативного мобильного приложения для iOS и Android",
        "price": 80000.0,
        "category": "IT услуги"
    },
    {
        "name": "Дизайн интерьера",
        "description": "Создание дизайн-проекта интерьера с 3D визуализацией",
        "price": 35000.0,
        "category": "Дизайн"
    }
]

def add_service(service_data):
    """Добавление услуги через API"""
    try:
        response = requests.post(
            f"{BASE_URL}/services",
            json=service_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ Добавлена: {service_data['name']}")
                return True
            else:
                print(f"❌ Ошибка: {result.get('message', 'Неизвестная ошибка')}")
                return False
        else:
            print(f"❌ HTTP ошибка {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Ошибка соединения: {e}")
        return False

def get_services():
    """Получение списка услуг"""
    try:
        response = requests.get(f"{BASE_URL}/services", timeout=10)
        if response.status_code == 200:
            return response.json().get("services", [])
        else:
            print(f"❌ Ошибка получения услуг: HTTP {response.status_code}")
            return []
    except requests.exceptions.RequestException as e:
        print(f"❌ Ошибка соединения: {e}")
        return []

def main():
    """Основная функция"""
    print("🚀 Демонстрация добавления услуг через API\n")
    
    # Проверяем доступность сервера
    print("📡 Проверка доступности сервера...")
    services = get_services()
    if services is None:
        print("❌ Сервер недоступен. Убедитесь, что сервер запущен на http://localhost:8000")
        return
    
    print(f"✅ Сервер доступен. Текущее количество услуг: {len(services)}\n")
    
    # Добавляем демонстрационные услуги
    print("📝 Добавление демонстрационных услуг:")
    added_count = 0
    
    for i, service in enumerate(DEMO_SERVICES, 1):
        print(f"{i}/{len(DEMO_SERVICES)}: ", end="")
        if add_service(service):
            added_count += 1
        time.sleep(0.5)  # Небольшая пауза между запросами
    
    print(f"\n✨ Добавлено услуг: {added_count}/{len(DEMO_SERVICES)}")
    
    # Получаем финальный список
    print("\n📊 Финальный список услуг:")
    final_services = get_services()
    
    if final_services:
        print(f"Всего услуг: {len(final_services)}\n")
        
        # Группируем по категориям
        categories = {}
        for service in final_services:
            category = service.get("category", "Без категории")
            if category not in categories:
                categories[category] = []
            categories[category].append(service)
        
        for category, services_in_category in categories.items():
            print(f"📂 {category} ({len(services_in_category)} услуг):")
            for service in services_in_category:
                print(f"   • {service['name']} - {service['price']} ₽")
            print()
    
    print("🎉 Демонстрация завершена!")
    print("🌐 Откройте http://localhost:8000 в браузере для просмотра веб-интерфейса")

if __name__ == "__main__":
    main()
