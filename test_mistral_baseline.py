#!/usr/bin/env python3
"""
Тестирование Mistral AI без MCP контекста - базовый тест
Проверяем сколько токенов тратится на ответы из собственной базы знаний
"""

import asyncio
import json
import time
from datetime import datetime

import httpx

# Mistral AI настройки
MISTRAL_API_KEY = "uEvRJbFSHPahqcULTbqMH3NQ0t9ASn71"
MISTRAL_MODEL = "mistral-small-latest"
MISTRAL_API_URL = "https://api.mistral.ai/v1/chat/completions"

class BaselineMistralTest:
    """Базовый тест Mistral AI без внешнего контекста"""
    
    def __init__(self):
        self.client = httpx.AsyncClient()
        self.results = []
    
    def count_tokens_estimate(self, text: str) -> int:
        """Приблизительный подсчет токенов"""
        return len(text) // 3
    
    async def test_mistral_baseline(self, user_message: str, test_name: str) -> dict:
        """Тестирование запроса к Mistral AI без контекста"""
        print(f"\n🧪 Тест: {test_name}")
        print(f"📝 Запрос: {user_message}")
        
        # Простой системный промпт без контекста услуг
        system_prompt = """Ты - AI ассистент. Отвечай на вопросы пользователя на русском языке, будь дружелюбным и полезным."""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        # Подсчитываем входящие токены
        input_text = system_prompt + user_message
        estimated_input_tokens = self.count_tokens_estimate(input_text)
        
        headers = {
            "Authorization": f"Bearer {MISTRAL_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": MISTRAL_MODEL,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                MISTRAL_API_URL,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                # Извлекаем информацию об использовании токенов
                usage = data.get("usage", {})
                prompt_tokens = usage.get("prompt_tokens", 0)
                completion_tokens = usage.get("completion_tokens", 0)
                total_tokens = usage.get("total_tokens", 0)
                
                response_text = data["choices"][0]["message"]["content"]
                estimated_output_tokens = self.count_tokens_estimate(response_text)
                
                result = {
                    "test_name": test_name,
                    "timestamp": datetime.now().isoformat(),
                    "user_message": user_message,
                    "response_time": response_time,
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens,
                    "estimated_input_tokens": estimated_input_tokens,
                    "estimated_output_tokens": estimated_output_tokens,
                    "response_length": len(response_text),
                    "response_text": response_text,
                    "system_prompt_length": len(system_prompt)
                }
                
                self.results.append(result)
                
                print(f"✅ Ответ получен за {response_time:.2f}с")
                print(f"📊 Токены: {prompt_tokens} вход + {completion_tokens} выход = {total_tokens} всего")
                print(f"📏 Оценка: {estimated_input_tokens} вход + {estimated_output_tokens} выход")
                print(f"📝 Ответ ({len(response_text)} символов): {response_text[:150]}...")
                
                return result
                
            else:
                print(f"❌ Ошибка API: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Ошибка запроса: {e}")
            return None
    
    async def run_baseline_tests(self):
        """Запуск базовых тестов без контекста"""
        print("🚀 Тестирование Mistral AI БЕЗ контекста услуг")
        print("=" * 60)
        
        # Тестовые запросы про услуги
        test_cases = [
            {
                "name": "Простой вопрос об услугах",
                "message": "Какие у вас услуги?"
            },
            {
                "name": "Вопрос про IT услуги",
                "message": "Покажи все IT услуги и их цены"
            },
            {
                "name": "Создание интернет-магазина",
                "message": "Мне нужно создать интернет-магазин с нуля. Что вы можете предложить и сколько это будет стоить?"
            },
            {
                "name": "Мобильное приложение",
                "message": "Нужна помощь с мобильным приложением для iOS и Android, плюс дизайн и маркетинг"
            },
            {
                "name": "Краткий вопрос",
                "message": "Цена сайта?"
            },
            {
                "name": "Консультация для стартапа",
                "message": "Я стартап в сфере финтех, нужен полный пакет услуг: от создания MVP до маркетинга и продвижения. Какие услуги вы рекомендуете?"
            }
        ]
        
        # Выполняем тесты
        for test_case in test_cases:
            await self.test_mistral_baseline(test_case["message"], test_case["name"])
            await asyncio.sleep(1)  # Пауза между запросами
        
        # Выводим сравнительную статистику
        self.print_comparison()
    
    def print_comparison(self):
        """Вывод сравнительной статистики"""
        print("\n" + "=" * 60)
        print("📊 СТАТИСТИКА MISTRAL AI БЕЗ КОНТЕКСТА")
        print("=" * 60)
        
        total_requests = len(self.results)
        total_input_tokens = sum(r["prompt_tokens"] for r in self.results)
        total_output_tokens = sum(r["completion_tokens"] for r in self.results)
        total_tokens = total_input_tokens + total_output_tokens
        
        print(f"🔢 Всего запросов: {total_requests}")
        print(f"📥 Всего входящих токенов: {total_input_tokens:,}")
        print(f"📤 Всего исходящих токенов: {total_output_tokens:,}")
        print(f"📊 Общий расход токенов: {total_tokens:,}")
        
        if total_requests > 0:
            avg_input = total_input_tokens / total_requests
            avg_output = total_output_tokens / total_requests
            avg_total = total_tokens / total_requests
            
            print(f"\n📈 Средние значения на запрос:")
            print(f"   📥 Входящие токены: {avg_input:.1f}")
            print(f"   📤 Исходящие токены: {avg_output:.1f}")
            print(f"   📊 Общие токены: {avg_total:.1f}")
        
        # Детализация по запросам
        print(f"\n📋 Детализация по запросам:")
        for result in self.results:
            print(f"   {result['test_name']}: {result['total_tokens']} токенов "
                  f"({result['prompt_tokens']}+{result['completion_tokens']}) "
                  f"за {result['response_time']:.2f}с")
        
        # Стоимость
        input_cost = (total_input_tokens / 1000) * 0.002
        output_cost = (total_output_tokens / 1000) * 0.006
        total_cost = input_cost + output_cost
        
        print(f"\n💰 Примерная стоимость (USD):")
        print(f"   📥 Входящие токены: ${input_cost:.4f}")
        print(f"   📤 Исходящие токены: ${output_cost:.4f}")
        print(f"   💵 Общая стоимость: ${total_cost:.4f}")
        
        # Анализ ответов
        print(f"\n🔍 Анализ ответов:")
        for result in self.results:
            response_preview = result['response_text'][:100].replace('\n', ' ')
            print(f"   {result['test_name']}: {response_preview}...")
        
        # Сравнение с MCP версией (если есть лог)
        try:
            with open("mistral_tokens_log.json", "r", encoding="utf-8") as f:
                mcp_data = json.load(f)
                
            print(f"\n🔄 СРАВНЕНИЕ С MCP ВЕРСИЕЙ:")
            print(f"   БЕЗ контекста: {avg_total:.1f} токенов в среднем")
            print(f"   С MCP контекстом: {mcp_data['summary']['total_input_tokens'] + mcp_data['summary']['total_output_tokens']:.1f} / {mcp_data['summary']['total_requests']} = {(mcp_data['summary']['total_input_tokens'] + mcp_data['summary']['total_output_tokens']) / mcp_data['summary']['total_requests']:.1f} токенов в среднем")
            
            savings = ((mcp_data['summary']['total_input_tokens'] + mcp_data['summary']['total_output_tokens']) / mcp_data['summary']['total_requests']) - avg_total
            print(f"   💰 Экономия БЕЗ контекста: {savings:.1f} токенов на запрос ({savings/avg_total*100:.1f}%)")
            
        except FileNotFoundError:
            print(f"\n💡 Запустите test_mistral_tokens.py для сравнения с MCP версией")
        
        # Сохраняем результаты
        with open("mistral_baseline_log.json", "w", encoding="utf-8") as f:
            json.dump({
                "summary": {
                    "total_requests": total_requests,
                    "total_input_tokens": total_input_tokens,
                    "total_output_tokens": total_output_tokens,
                    "total_cost_usd": total_cost,
                    "avg_tokens_per_request": avg_total
                },
                "results": self.results
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Результаты сохранены в mistral_baseline_log.json")
    
    async def close(self):
        """Закрытие HTTP клиента"""
        await self.client.aclose()

async def main():
    """Основная функция"""
    tester = BaselineMistralTest()
    try:
        await tester.run_baseline_tests()
    finally:
        await tester.close()

if __name__ == "__main__":
    asyncio.run(main())
