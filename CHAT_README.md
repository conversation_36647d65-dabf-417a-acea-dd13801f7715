# 💬 Чат-консультант по услугам

Минималистичный чат-интерфейс с интеграцией Mistral AI для консультаций по базе услуг.

## 🎯 Особенности

- **Mistral AI** - использует модель `mistral-small-latest`
- **Real-time консультации** - мгновенные ответы по услугам
- **Интеграция с MCP** - получает актуальные данные из базы услуг
- **Bootstrap UI** - минималистичный и адаптивный дизайн
- **Контекстные ответы** - AI знает все услуги и их детали

## 🚀 Быстрый запуск

### Вариант 1: Запуск всей системы
```bash
# Активация окружения
source venv/bin/activate

# Запуск обоих серверов одновременно
python start_all.py
```

### Вариант 2: Ручной запуск

1. **Запуск MCP сервера услуг:**
```bash
source venv/bin/activate
python main.py
```

2. **Запуск чат-сервера (в новом терминале):**
```bash
source venv/bin/activate
python chat_server.py
```

## 🌐 Доступ к интерфейсам

- **📊 Управление услугами:** http://localhost:8000
- **💬 Чат-консультант:** http://localhost:8001

## 🤖 Возможности чата

### Что умеет AI консультант:

✅ **Поиск услуг по категориям**
- "Какие у вас IT услуги?"
- "Покажи услуги по дизайну"
- "Что есть в категории маркетинг?"

✅ **Информация о ценах**
- "Сколько стоит создание сайта?"
- "Какие цены на консультации?"
- "Самые дорогие услуги"

✅ **Детальные описания**
- "Расскажи про веб-разработку"
- "Что включает SEO оптимизация?"
- "Подробности про мобильные приложения"

✅ **Рекомендации**
- "Нужна помощь с бизнесом"
- "Хочу создать интернет-магазин"
- "Посоветуй услуги для стартапа"

✅ **Комплексные решения**
- "Нужен полный пакет для запуска бизнеса"
- "Что нужно для создания бренда?"

## 🔧 Технические детали

### Архитектура
```
Пользователь → Чат UI → FastAPI → Mistral AI → MCP API → База услуг
```

### Компоненты

1. **chat_server.py** - FastAPI сервер чата
2. **MistralClient** - клиент для работы с Mistral AI
3. **Bootstrap UI** - веб-интерфейс чата
4. **MCP Integration** - получение данных об услугах

### API Endpoints

- `GET /` - веб-интерфейс чата
- `POST /chat` - отправка сообщения
- `GET /health` - проверка здоровья

### Настройки

```python
MISTRAL_API_KEY = "uEvRJbFSHPahqcULTbqMH3NQ0t9ASn71"
MISTRAL_MODEL = "mistral-small-latest"
MCP_SERVER_URL = "http://localhost:8000"
```

## 💡 Примеры диалогов

### Поиск услуг
```
👤 Пользователь: Какие у вас IT услуги?
🤖 Консультант: У нас есть отличные IT услуги:

📂 IT услуги:
• Консультация по Python - 2000 ₽ (ID: 1)
• Разработка веб-сайта - 50000 ₽ (ID: 3)  
• Настройка сервера - 8000 ₽ (ID: 7)
• Мобильное приложение - 80000 ₽ (ID: 9)
• Веб-разработка - 45000 ₽ (ID: 11)

Какая услуга вас интересует больше всего?
```

### Консультация по цене
```
👤 Пользователь: Сколько стоит создание сайта?
🤖 Консультант: Создание сайта у нас стоит 50000 ₽ (ID: 3).

Это включает создание современного адаптивного веб-сайта с использованием последних технологий. 

Также есть более доступный вариант "Веб-разработка" за 45000 ₽ (ID: 11) - создание современных веб-приложений.

Хотите узнать подробности или есть вопросы по функционалу?
```

## 🛠️ Разработка

### Добавление новых функций

1. **Расширение промптов** - редактируйте `system_prompt` в `chat_server.py`
2. **Новые API endpoints** - добавляйте в FastAPI приложение
3. **UI улучшения** - модифицируйте HTML в функции `chat_page()`

### Интеграция с другими AI

Замените `MistralClient` на другой провайдер:
- OpenAI GPT
- Anthropic Claude  
- Google Gemini
- Локальные модели

## 🚨 Troubleshooting

### Чат не отвечает
1. Проверьте API ключ Mistral AI
2. Убедитесь, что MCP сервер запущен
3. Проверьте интернет-соединение

### Нет данных об услугах
1. Запустите MCP сервер: `python main.py`
2. Добавьте тестовые услуги: `python demo_services.py`
3. Проверьте доступность: `curl http://localhost:8000/api/services`

### Ошибки в логах
```bash
# Проверка логов MCP сервера
tail -f mcp_server.log

# Проверка логов чат-сервера  
tail -f chat_server.log
```

## 🎉 Готово!

Теперь у вас есть полнофункциональный AI-консультант, который:
- ✅ Знает все ваши услуги
- ✅ Отвечает на русском языке
- ✅ Дает точные цены и описания
- ✅ Предлагает подходящие решения
- ✅ Работает в real-time

**Приятного общения с AI-консультантом! 🤖💬**
