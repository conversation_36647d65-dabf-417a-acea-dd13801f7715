#!/usr/bin/env python3
"""
Скрипт для запуска MCP сервера и чат-сервера одновременно
"""

import asyncio
import subprocess
import sys
import time
from pathlib import Path

def start_mcp_server():
    """Запуск MCP сервера"""
    print("🚀 Запуск MCP сервера услуг...")
    return subprocess.Popen([
        sys.executable, "main.py"
    ])

def start_chat_server():
    """Запуск чат-сервера"""
    print("💬 Запуск чат-сервера...")
    return subprocess.Popen([
        sys.executable, "chat_server.py"
    ])

def main():
    """Основная функция"""
    print("=" * 50)
    print("🎯 Запуск системы управления услугами")
    print("=" * 50)
    
    # Проверяем наличие файлов
    if not Path("main.py").exists():
        print("❌ Файл main.py не найден!")
        return
    
    if not Path("chat_server.py").exists():
        print("❌ Файл chat_server.py не найден!")
        return
    
    mcp_process = None
    chat_process = None
    
    try:
        # Запускаем MCP сервер
        mcp_process = start_mcp_server()
        time.sleep(3)  # Даем время на запуск
        
        # Проверяем, что MCP сервер запустился
        if mcp_process.poll() is not None:
            print("❌ Ошибка запуска MCP сервера")
            return
        
        print("✅ MCP сервер запущен на http://localhost:8000")
        
        # Запускаем чат-сервер
        chat_process = start_chat_server()
        time.sleep(2)  # Даем время на запуск
        
        # Проверяем, что чат-сервер запустился
        if chat_process.poll() is not None:
            print("❌ Ошибка запуска чат-сервера")
            return
        
        print("✅ Чат-сервер запущен на http://localhost:8001")
        print()
        print("🌐 Доступные интерфейсы:")
        print("   📊 Управление услугами: http://localhost:8000")
        print("   💬 Чат-консультант:     http://localhost:8001")
        print()
        print("⏹️  Нажмите Ctrl+C для остановки серверов")
        
        # Ждем сигнала остановки
        while True:
            time.sleep(1)
            
            # Проверяем, что процессы еще живы
            if mcp_process.poll() is not None:
                print("❌ MCP сервер остановился")
                break
            
            if chat_process.poll() is not None:
                print("❌ Чат-сервер остановился")
                break
    
    except KeyboardInterrupt:
        print("\n🛑 Получен сигнал остановки...")
    
    except Exception as e:
        print(f"❌ Ошибка: {e}")
    
    finally:
        # Останавливаем процессы
        if mcp_process and mcp_process.poll() is None:
            print("🔄 Остановка MCP сервера...")
            mcp_process.terminate()
            try:
                mcp_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                mcp_process.kill()
        
        if chat_process and chat_process.poll() is None:
            print("🔄 Остановка чат-сервера...")
            chat_process.terminate()
            try:
                chat_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                chat_process.kill()
        
        print("✅ Все серверы остановлены")

if __name__ == "__main__":
    main()
