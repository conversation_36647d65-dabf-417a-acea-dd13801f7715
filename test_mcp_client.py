#!/usr/bin/env python3
"""
Простой MCP клиент для тестирования нашего сервера
"""

import asyncio
import json
import sys
from pathlib import Path

# Добавляем текущую директорию в путь
sys.path.insert(0, str(Path(__file__).parent))

from mcp.client.session import ClientSession
from mcp.client.stdio import stdio_client

async def test_mcp_server():
    """Тестирование MCP сервера через stdio"""
    print("🧪 Тестирование MCP сервера через stdio клиент")
    
    # Запускаем сервер как subprocess
    server_params = {
        "command": "python",
        "args": ["mcp_stdio.py"],
        "cwd": str(Path(__file__).parent),
    }
    
    try:
        async with stdio_client(server_params) as (read, write):
            async with <PERSON>lient<PERSON><PERSON><PERSON>(read, write) as session:
                print("✅ Подключение к MCP серверу установлено")
                
                # Инициализация
                await session.initialize()
                print("✅ Инициализация завершена")
                
                # Получаем список tools
                tools = await session.list_tools()
                print(f"✅ Найдено tools: {len(tools.tools)}")
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")
                
                # Получаем список resources
                resources = await session.list_resources()
                print(f"✅ Найдено resources: {len(resources.resources)}")
                for resource in resources.resources:
                    print(f"   - {resource.uri}: {resource.name}")
                
                # Получаем список prompts
                prompts = await session.list_prompts()
                print(f"✅ Найдено prompts: {len(prompts.prompts)}")
                for prompt in prompts.prompts:
                    print(f"   - {prompt.name}: {prompt.description}")
                
                # Тестируем tool
                print("\n🔧 Тестирование tool 'list_services':")
                result = await session.call_tool("list_services", {})
                print(f"   Результат: {result.content[0].text[:100]}...")
                
                # Тестируем resource
                print("\n📊 Тестирование resource 'services://all':")
                from pydantic import AnyUrl
                resource_result = await session.read_resource(AnyUrl("services://all"))
                print(f"   Получено данных: {len(resource_result.contents[0].text)} символов")
                
                print("\n🎉 Все тесты MCP прошли успешно!")
                
    except Exception as e:
        print(f"❌ Ошибка тестирования MCP: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mcp_server())
