#!/usr/bin/env python3
"""
Тестирование расхода токенов при обращении к Mistral AI
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Dict, List

import httpx

# Mistral AI настройки
MISTRAL_API_KEY = "uEvRJbFSHPahqcULTbqMH3NQ0t9ASn71"
MISTRAL_MODEL = "mistral-small-latest"
MISTRAL_API_URL = "https://api.mistral.ai/v1/chat/completions"

# URL нашего MCP сервера
MCP_SERVER_URL = "http://localhost:8000"

class TokenUsageTracker:
    """Трекер использования токенов"""
    
    def __init__(self):
        self.client = httpx.AsyncClient()
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_requests = 0
        self.requests_log = []
    
    async def get_services_data(self) -> List[Dict]:
        """Получение данных об услугах из MCP сервера"""
        try:
            response = await self.client.get(f"{MCP_SERVER_URL}/api/services")
            if response.status_code == 200:
                data = response.json()
                return data.get("services", [])
            else:
                print(f"❌ Ошибка получения услуг: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Ошибка соединения с MCP сервером: {e}")
            return []
    
    def format_services_for_context(self, services: List[Dict]) -> str:
        """Форматирование услуг для контекста AI"""
        if not services:
            return "База услуг пуста."
        
        context = "ДОСТУПНЫЕ УСЛУГИ:\n\n"
        
        # Группируем по категориям
        categories = {}
        for service in services:
            category = service.get("category", "Общие")
            if category not in categories:
                categories[category] = []
            categories[category].append(service)
        
        for category, category_services in categories.items():
            context += f"📂 {category}:\n"
            for service in category_services:
                status = "✅ Активна" if service.get("active", True) else "❌ Неактивна"
                context += f"  • {service['name']} - {service['price']} ₽ ({status})\n"
                context += f"    Описание: {service['description']}\n"
                context += f"    ID: {service['id']}\n\n"
        
        return context
    
    def count_tokens_estimate(self, text: str) -> int:
        """Приблизительный подсчет токенов (1 токен ≈ 4 символа для русского текста)"""
        return len(text) // 3  # Более точная оценка для русского текста
    
    async def test_mistral_request(self, user_message: str, test_name: str) -> Dict:
        """Тестирование одного запроса к Mistral AI"""
        print(f"\n🧪 Тест: {test_name}")
        print(f"📝 Запрос: {user_message}")
        
        # Получаем актуальные данные об услугах
        services = await self.get_services_data()
        services_context = self.format_services_for_context(services)
        
        # Системный промпт с контекстом услуг
        system_prompt = f"""Ты - консультант по услугам компании. Твоя задача помочь клиентам выбрать подходящие услуги.

{services_context}

ПРАВИЛА:
1. Отвечай только на русском языке
2. Будь дружелюбным и профессиональным
3. Предлагай конкретные услуги из базы данных
4. Указывай точные цены и описания
5. Если услуги нет в базе - честно скажи об этом
6. Можешь рекомендовать несколько услуг для комплексного решения
7. Всегда указывай ID услуги для заказа

Отвечай кратко и по делу. Не придумывай услуги, которых нет в базе."""

        # Подготавливаем сообщения для API
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]
        
        # Подсчитываем входящие токены
        input_text = system_prompt + user_message
        estimated_input_tokens = self.count_tokens_estimate(input_text)
        
        headers = {
            "Authorization": f"Bearer {MISTRAL_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": MISTRAL_MODEL,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        start_time = time.time()
        
        try:
            response = await self.client.post(
                MISTRAL_API_URL,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                
                # Извлекаем информацию об использовании токенов
                usage = data.get("usage", {})
                prompt_tokens = usage.get("prompt_tokens", 0)
                completion_tokens = usage.get("completion_tokens", 0)
                total_tokens = usage.get("total_tokens", 0)
                
                response_text = data["choices"][0]["message"]["content"]
                estimated_output_tokens = self.count_tokens_estimate(response_text)
                
                # Обновляем статистику
                self.total_input_tokens += prompt_tokens
                self.total_output_tokens += completion_tokens
                self.total_requests += 1
                
                # Логируем запрос
                request_log = {
                    "test_name": test_name,
                    "timestamp": datetime.now().isoformat(),
                    "user_message": user_message,
                    "response_time": response_time,
                    "prompt_tokens": prompt_tokens,
                    "completion_tokens": completion_tokens,
                    "total_tokens": total_tokens,
                    "estimated_input_tokens": estimated_input_tokens,
                    "estimated_output_tokens": estimated_output_tokens,
                    "response_length": len(response_text),
                    "services_count": len(services),
                    "context_length": len(services_context)
                }
                
                self.requests_log.append(request_log)
                
                print(f"✅ Ответ получен за {response_time:.2f}с")
                print(f"📊 Токены: {prompt_tokens} вход + {completion_tokens} выход = {total_tokens} всего")
                print(f"📏 Оценка: {estimated_input_tokens} вход + {estimated_output_tokens} выход")
                print(f"📝 Ответ ({len(response_text)} символов): {response_text[:100]}...")
                
                return request_log
                
            else:
                print(f"❌ Ошибка API: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Ошибка запроса: {e}")
            return None
    
    async def run_token_tests(self):
        """Запуск серии тестов для анализа расхода токенов"""
        print("🚀 Начинаем тестирование расхода токенов Mistral AI")
        print("=" * 60)
        
        # Тестовые запросы разной сложности
        test_cases = [
            {
                "name": "Простой запрос",
                "message": "Какие у вас услуги?"
            },
            {
                "name": "Запрос по категории",
                "message": "Покажи все IT услуги и их цены"
            },
            {
                "name": "Комплексная консультация",
                "message": "Мне нужно создать интернет-магазин с нуля. Что вы можете предложить и сколько это будет стоить?"
            },
            {
                "name": "Специфический запрос",
                "message": "Нужна помощь с мобильным приложением для iOS и Android, плюс дизайн и маркетинг"
            },
            {
                "name": "Краткий запрос",
                "message": "Цена сайта?"
            },
            {
                "name": "Детальная консультация",
                "message": "Я стартап в сфере финтех, нужен полный пакет услуг: от создания MVP до маркетинга и продвижения. Какие услуги вы рекомендуете и в какой последовательности? Также интересует общая стоимость проекта."
            }
        ]
        
        # Выполняем тесты
        for test_case in test_cases:
            await self.test_mistral_request(test_case["message"], test_case["name"])
            await asyncio.sleep(1)  # Пауза между запросами
        
        # Выводим итоговую статистику
        self.print_summary()
    
    def print_summary(self):
        """Вывод итоговой статистики"""
        print("\n" + "=" * 60)
        print("📊 ИТОГОВАЯ СТАТИСТИКА ИСПОЛЬЗОВАНИЯ ТОКЕНОВ")
        print("=" * 60)
        
        print(f"🔢 Всего запросов: {self.total_requests}")
        print(f"📥 Всего входящих токенов: {self.total_input_tokens:,}")
        print(f"📤 Всего исходящих токенов: {self.total_output_tokens:,}")
        print(f"📊 Общий расход токенов: {self.total_input_tokens + self.total_output_tokens:,}")
        
        if self.total_requests > 0:
            avg_input = self.total_input_tokens / self.total_requests
            avg_output = self.total_output_tokens / self.total_requests
            avg_total = (self.total_input_tokens + self.total_output_tokens) / self.total_requests
            
            print(f"\n📈 Средние значения на запрос:")
            print(f"   📥 Входящие токены: {avg_input:.1f}")
            print(f"   📤 Исходящие токены: {avg_output:.1f}")
            print(f"   📊 Общие токены: {avg_total:.1f}")
        
        # Анализ по типам запросов
        print(f"\n📋 Детализация по запросам:")
        for log in self.requests_log:
            print(f"   {log['test_name']}: {log['total_tokens']} токенов "
                  f"({log['prompt_tokens']}+{log['completion_tokens']}) "
                  f"за {log['response_time']:.2f}с")
        
        # Стоимость (примерная для Mistral AI)
        # Mistral Small: $0.002 за 1K токенов (input), $0.006 за 1K токенов (output)
        input_cost = (self.total_input_tokens / 1000) * 0.002
        output_cost = (self.total_output_tokens / 1000) * 0.006
        total_cost = input_cost + output_cost
        
        print(f"\n💰 Примерная стоимость (USD):")
        print(f"   📥 Входящие токены: ${input_cost:.4f}")
        print(f"   📤 Исходящие токены: ${output_cost:.4f}")
        print(f"   💵 Общая стоимость: ${total_cost:.4f}")
        
        # Сохраняем детальный лог
        with open("mistral_tokens_log.json", "w", encoding="utf-8") as f:
            json.dump({
                "summary": {
                    "total_requests": self.total_requests,
                    "total_input_tokens": self.total_input_tokens,
                    "total_output_tokens": self.total_output_tokens,
                    "total_cost_usd": total_cost
                },
                "requests": self.requests_log
            }, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 Детальный лог сохранен в mistral_tokens_log.json")
    
    async def close(self):
        """Закрытие HTTP клиента"""
        await self.client.aclose()

async def main():
    """Основная функция"""
    tracker = TokenUsageTracker()
    try:
        await tracker.run_token_tests()
    finally:
        await tracker.close()

if __name__ == "__main__":
    asyncio.run(main())
