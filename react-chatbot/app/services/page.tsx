"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Trash2, Plus, RefreshCw, MessageCircle, Moon, Sun } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

interface Service {
  id: number
  name: string
  description: string
  price: number
  category: string
  active: boolean
  created_at: string
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isDark, setIsDark] = useState(true)
  const { toast } = useToast()

  // Форма добавления услуги
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    price: "",
    category: "Общие"
  })

  // Применяем темную тему к документу
  useEffect(() => {
    if (isDark) {
      document.documentElement.classList.add("dark")
    } else {
      document.documentElement.classList.remove("dark")
    }
  }, [isDark])

  // Загрузка услуг при монтировании компонента
  useEffect(() => {
    loadServices()
  }, [])

  const loadServices = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/services')
      const data = await response.json()
      setServices(data.services || [])
    } catch (error) {
      console.error('Ошибка загрузки услуг:', error)
      toast({
        title: "Ошибка",
        description: "Не удалось загрузить услуги. Проверьте подключение к серверу.",
        variant: "destructive"
      })
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim() || !formData.description.trim() || !formData.price) {
      toast({
        title: "Ошибка",
        description: "Пожалуйста, заполните все поля",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('http://localhost:8000/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          price: parseFloat(formData.price),
          category: formData.category
        })
      })

      const result = await response.json()
      
      if (result.success) {
        toast({
          title: "Успех!",
          description: `Услуга "${formData.name}" успешно добавлена`,
        })
        
        // Очищаем форму
        setFormData({
          name: "",
          description: "",
          price: "",
          category: "Общие"
        })
        
        // Перезагружаем список услуг
        await loadServices()
      } else {
        toast({
          title: "Ошибка",
          description: result.message || "Не удалось добавить услугу",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Ошибка при добавлении услуги:', error)
      toast({
        title: "Ошибка",
        description: "Ошибка соединения с сервером",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const deleteService = async (serviceId: number) => {
    if (!confirm('Вы уверены, что хотите удалить эту услугу?')) {
      return
    }

    try {
      const response = await fetch(`http://localhost:8000/api/services/${serviceId}`, {
        method: 'DELETE'
      })

      const result = await response.json()
      
      if (result.success) {
        toast({
          title: "Успех!",
          description: "Услуга успешно удалена",
        })
        await loadServices()
      } else {
        toast({
          title: "Ошибка",
          description: result.message || "Не удалось удалить услугу",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Ошибка при удалении услуги:', error)
      toast({
        title: "Ошибка",
        description: "Ошибка соединения с сервером",
        variant: "destructive"
      })
    }
  }

  const toggleTheme = () => {
    setIsDark(!isDark)
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB'
    }).format(price)
  }

  return (
    <div className="min-h-screen bg-background transition-colors duration-300 p-4">
      <div className="mx-auto max-w-6xl">
        {/* Заголовок */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Управление услугами</h1>
            <p className="text-muted-foreground mt-1">Добавляйте и управляйте услугами вашей компании</p>
          </div>
          <div className="flex gap-2">
            <Link href="/">
              <Button variant="outline" className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                Чат
              </Button>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="text-muted-foreground hover:text-foreground"
            >
              {isDark ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Форма добавления услуги */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="h-5 w-5" />
                  Добавить услугу
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <Label htmlFor="name">Название услуги</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="Введите название услуги"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Описание</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      placeholder="Опишите услугу"
                      rows={3}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="price">Цена (руб.)</Label>
                    <Input
                      id="price"
                      type="number"
                      step="0.01"
                      value={formData.price}
                      onChange={(e) => setFormData({...formData, price: e.target.value})}
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="category">Категория</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Выберите категорию" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Общие">Общие</SelectItem>
                        <SelectItem value="IT услуги">IT услуги</SelectItem>
                        <SelectItem value="Консультации">Консультации</SelectItem>
                        <SelectItem value="Дизайн">Дизайн</SelectItem>
                        <SelectItem value="Маркетинг">Маркетинг</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Button type="submit" disabled={isLoading} className="w-full">
                    {isLoading ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Добавление...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Добавить услугу
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Список услуг */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Список услуг ({services.length})</CardTitle>
                  <Button variant="outline" size="sm" onClick={loadServices}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Обновить
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {services.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>Услуги не найдены</p>
                    <p className="text-sm mt-1">Добавьте первую услугу, используя форму слева</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {services.map((service) => (
                      <div key={service.id} className="border border-border rounded-lg p-4 hover:bg-accent/50 transition-colors">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-foreground">{service.name}</h3>
                              <Badge variant="secondary">{service.category}</Badge>
                              {service.active && <Badge variant="default">Активна</Badge>}
                            </div>
                            <p className="text-muted-foreground text-sm mb-2">{service.description}</p>
                            <div className="flex items-center justify-between">
                              <span className="text-lg font-bold text-primary">{formatPrice(service.price)}</span>
                              <span className="text-xs text-muted-foreground">ID: {service.id}</span>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteService(service.id)}
                            className="text-destructive hover:text-destructive hover:bg-destructive/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
