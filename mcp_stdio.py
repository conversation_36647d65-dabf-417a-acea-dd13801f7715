#!/usr/bin/env python3
"""
MCP сервер в режиме stdio для подключения к Windsurf
"""

import sys
from pathlib import Path

# Добавляем текущую директорию в путь
sys.path.insert(0, str(Path(__file__).parent))

from main import mcp, init_services_data

if __name__ == "__main__":
    # Инициализация данных
    init_services_data()
    
    # Запуск в режиме stdio для MCP клиентов
    print("Запуск MCP сервера в режиме stdio...", file=sys.stderr)
    mcp.run(transport="stdio")
