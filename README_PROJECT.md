# FastMCP Services Manager

Веб-приложение для управления услугами на основе MCP Python SDK с FastAPI, Bootstrap и Server-Sent Events (SSE).

## Особенности

- 🚀 **FastMCP сервер** - современный MCP сервер с веб-интерфейсом
- 🌐 **Веб-интерфейс** - красивый UI на Bootstrap 5
- ⚡ **Real-time обновления** - Server-Sent Events для мгновенных обновлений
- 🛠️ **MCP Tools** - полный набор инструментов для управления услугами
- 📊 **Управление данными** - JSON-based хранение с автоматическим бэкапом
- 🎨 **Адаптивный дизайн** - работает на всех устройствах

## Структура проекта

```
python-mcp/
├── main.py                 # Основной FastMCP сервер
├── static/
│   └── app.js             # JavaScript для веб-интерфейса
├── services/
│   ├── __init__.py
│   └── manager.py         # Менеджер услуг
├── requirements.txt       # Зависимости Python
├── services_data.json     # Данные услуг (создается автоматически)
└── README_PROJECT.md      # Этот файл
```

## Установка и запуск

### 1. Подготовка окружения

```bash
# Активация виртуального окружения (если еще не активировано)
source venv/bin/activate

# Установка зависимостей
pip install -r requirements.txt
```

### 2. Запуск сервера

```bash
# Запуск с SSE транспортом
python main.py
```

Сервер будет доступен по адресу: http://localhost:8000

### 3. Альтернативные способы запуска

```bash
# Запуск с stdio транспортом (для MCP клиентов)
python -c "from main import mcp; mcp.run(transport='stdio')"

# Запуск с streamable-http транспортом
python -c "from main import mcp; mcp.run(transport='streamable-http')"
```

## Использование

### Веб-интерфейс

1. Откройте браузер и перейдите на http://localhost:8000
2. Используйте форму для добавления новых услуг
3. Просматривайте и управляйте существующими услугами
4. Получайте real-time обновления через SSE

### MCP Tools

Сервер предоставляет следующие MCP инструменты:

#### `add_service`
Добавление новой услуги
```json
{
  "name": "Консультация по Python",
  "description": "Помощь в изучении Python",
  "price": 2000.0,
  "category": "IT услуги"
}
```

#### `remove_service`
Удаление услуги по ID
```json
{
  "service_id": 1
}
```

#### `list_services`
Получение списка всех услуг (без параметров)

#### `update_service`
Обновление данных услуги
```json
{
  "service_id": 1,
  "name": "Новое название",
  "price": 2500.0,
  "active": true
}
```

### API Endpoints

#### GET `/api/services`
Получение списка услуг в JSON формате

#### POST `/api/services`
Добавление новой услуги
```json
{
  "name": "Название услуги",
  "description": "Описание услуги",
  "price": 1000.0,
  "category": "Категория"
}
```

#### DELETE `/api/services/{service_id}`
Удаление услуги по ID

#### GET `/sse`
Server-Sent Events endpoint для real-time обновлений

## Конфигурация

### Настройки сервера

Основные настройки находятся в `main.py`:

- `SERVICES_FILE` - путь к файлу с данными услуг
- Порт сервера: 8000 (по умолчанию для SSE транспорта)

### Категории услуг

По умолчанию доступны следующие категории:
- Общие
- IT услуги
- Консультации
- Дизайн
- Маркетинг

Вы можете добавить новые категории, отредактировав HTML форму в `main.py`.

## Разработка

### Добавление новых функций

1. **Новые MCP Tools**: Добавьте функции с декоратором `@mcp.tool()` в `main.py`
2. **Новые API endpoints**: Используйте `@mcp.custom_route()` для создания новых маршрутов
3. **Расширение UI**: Модифицируйте HTML в функции `index()` и JavaScript в `static/app.js`

### Структура данных услуги

```json
{
  "id": 1,
  "name": "Название услуги",
  "description": "Описание услуги",
  "price": 1000.0,
  "category": "Категория",
  "created_at": "2024-01-01T12:00:00",
  "updated_at": "2024-01-01T12:00:00",
  "active": true
}
```

## Troubleshooting

### Проблемы с SSE

Если SSE соединение не работает:
1. Проверьте, что сервер запущен с транспортом "sse"
2. Убедитесь, что браузер поддерживает EventSource
3. Проверьте консоль браузера на наличие ошибок

### Проблемы с данными

Если данные не сохраняются:
1. Проверьте права на запись в директории проекта
2. Убедитесь, что файл `services_data.json` не поврежден
3. Проверьте логи сервера

### Порты

Если порт 8000 занят, измените порт в коде или используйте другой транспорт:
```bash
# Для streamable-http (порт 3000 по умолчанию)
python -c "from main import mcp; mcp.run(transport='streamable-http')"
```

## Лицензия

Проект основан на MCP Python SDK (MIT License).

## Поддержка

Для вопросов и предложений создавайте issues в репозитории проекта.
