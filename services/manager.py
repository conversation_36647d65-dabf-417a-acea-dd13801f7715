"""
Менеджер услуг для FastMCP сервера
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class ServiceManager:
    """Класс для управления услугами"""
    
    def __init__(self, data_file: str = "services_data.json"):
        self.data_file = Path(data_file)
        self.init_data_file()
    
    def init_data_file(self):
        """Инициализация файла с данными"""
        if not self.data_file.exists():
            initial_data = {
                "services": [],
                "last_updated": datetime.now().isoformat(),
                "next_id": 1
            }
            self.save_data(initial_data)
    
    def load_data(self) -> Dict[str, Any]:
        """Загрузка данных из файла"""
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Ошибка загрузки данных: {e}")
            return {"services": [], "last_updated": datetime.now().isoformat(), "next_id": 1}
    
    def save_data(self, data: Dict[str, Any]):
        """Сохранение данных в файл"""
        try:
            data["last_updated"] = datetime.now().isoformat()
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Ошибка сохранения данных: {e}")
    
    def get_services(self) -> List[Dict[str, Any]]:
        """Получение списка всех услуг"""
        data = self.load_data()
        return data.get("services", [])
    
    def get_service_by_id(self, service_id: int) -> Optional[Dict[str, Any]]:
        """Получение услуги по ID"""
        services = self.get_services()
        for service in services:
            if service.get("id") == service_id:
                return service
        return None
    
    def add_service(self, name: str, description: str, price: float, category: str = "Общие") -> Dict[str, Any]:
        """Добавление новой услуги"""
        data = self.load_data()
        services = data.get("services", [])
        
        # Проверка на дублирование имени
        for service in services:
            if service["name"].lower() == name.lower():
                raise ValueError(f"Услуга с именем '{name}' уже существует")
        
        # Создание новой услуги
        new_service = {
            "id": data.get("next_id", 1),
            "name": name,
            "description": description,
            "price": price,
            "category": category,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "active": True
        }
        
        services.append(new_service)
        data["services"] = services
        data["next_id"] = data.get("next_id", 1) + 1
        
        self.save_data(data)
        return new_service
    
    def update_service(self, service_id: int, **kwargs) -> Optional[Dict[str, Any]]:
        """Обновление услуги"""
        data = self.load_data()
        services = data.get("services", [])
        
        for i, service in enumerate(services):
            if service.get("id") == service_id:
                # Обновляем только переданные поля
                for key, value in kwargs.items():
                    if key in ["name", "description", "price", "category", "active"]:
                        service[key] = value
                
                service["updated_at"] = datetime.now().isoformat()
                services[i] = service
                data["services"] = services
                
                self.save_data(data)
                return service
        
        return None
    
    def delete_service(self, service_id: int) -> Optional[Dict[str, Any]]:
        """Удаление услуги"""
        data = self.load_data()
        services = data.get("services", [])
        
        for i, service in enumerate(services):
            if service.get("id") == service_id:
                deleted_service = services.pop(i)
                data["services"] = services
                self.save_data(data)
                return deleted_service
        
        return None
    
    def get_services_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Получение услуг по категории"""
        services = self.get_services()
        return [s for s in services if s.get("category", "").lower() == category.lower()]
    
    def get_active_services(self) -> List[Dict[str, Any]]:
        """Получение только активных услуг"""
        services = self.get_services()
        return [s for s in services if s.get("active", True)]
    
    def search_services(self, query: str) -> List[Dict[str, Any]]:
        """Поиск услуг по названию или описанию"""
        services = self.get_services()
        query_lower = query.lower()
        
        results = []
        for service in services:
            if (query_lower in service.get("name", "").lower() or 
                query_lower in service.get("description", "").lower() or
                query_lower in service.get("category", "").lower()):
                results.append(service)
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """Получение статистики по услугам"""
        services = self.get_services()
        
        if not services:
            return {
                "total_services": 0,
                "active_services": 0,
                "inactive_services": 0,
                "categories": {},
                "average_price": 0,
                "total_value": 0
            }
        
        active_services = [s for s in services if s.get("active", True)]
        categories = {}
        total_price = 0
        
        for service in services:
            category = service.get("category", "Общие")
            categories[category] = categories.get(category, 0) + 1
            total_price += service.get("price", 0)
        
        return {
            "total_services": len(services),
            "active_services": len(active_services),
            "inactive_services": len(services) - len(active_services),
            "categories": categories,
            "average_price": total_price / len(services) if services else 0,
            "total_value": total_price
        }


# Глобальный экземпляр менеджера
service_manager = ServiceManager()
