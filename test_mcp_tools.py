#!/usr/bin/env python3
"""
Тестирование MCP tools
"""

import asyncio
import sys
from pathlib import Path

# Добавляем текущую директорию в путь
sys.path.insert(0, str(Path(__file__).parent))

from main import mcp

async def test_mcp_tools():
    """Тестирование MCP инструментов"""
    print("=== Тестирование MCP Tools ===\n")
    
    # Тест 1: Получение списка услуг
    print("1. Получение списка услуг:")
    result = await mcp._tool_manager.call_tool("list_services", {})
    print(f"Результат: {result}\n")
    
    # Тест 2: Добавление новой услуги
    print("2. Добавление новой услуги:")
    result = await mcp._tool_manager.call_tool("add_service", {
        "name": "Веб-дизайн",
        "description": "Создание современных веб-сайтов",
        "price": 5000.0,
        "category": "Дизайн"
    })
    print(f"Результат: {result}\n")
    
    # Тест 3: Получение обновленного списка услуг
    print("3. Получение обновленного списка услуг:")
    result = await mcp._tool_manager.call_tool("list_services", {})
    print(f"Результат: {result}\n")
    
    # Тест 4: Обновление услуги
    print("4. Обновление услуги (ID=3):")
    result = await mcp._tool_manager.call_tool("update_service", {
        "service_id": 3,
        "price": 4500.0,
        "description": "Создание современных адаптивных веб-сайтов"
    })
    print(f"Результат: {result}\n")
    
    # Тест 5: Попытка добавить дублирующуюся услугу
    print("5. Попытка добавить дублирующуюся услугу:")
    result = await mcp._tool_manager.call_tool("add_service", {
        "name": "Веб-дизайн",
        "description": "Дублирующая услуга",
        "price": 3000.0
    })
    print(f"Результат: {result}\n")
    
    # Тест 6: Удаление услуги
    print("6. Удаление услуги (ID=2):")
    result = await mcp._tool_manager.call_tool("remove_service", {
        "service_id": 2
    })
    print(f"Результат: {result}\n")
    
    # Тест 7: Финальный список услуг
    print("7. Финальный список услуг:")
    result = await mcp._tool_manager.call_tool("list_services", {})
    print(f"Результат: {result}\n")

if __name__ == "__main__":
    asyncio.run(test_mcp_tools())
