#!/usr/bin/env python3
"""
Минималистичный чат-сервер с интеграцией Mistral AI и MCP
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Any, Dict, List

import httpx
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Mistral AI настройки
MISTRAL_API_KEY = "uEvRJbFSHPahqcULTbqMH3NQ0t9ASn71"
MISTRAL_MODEL = "mistral-small-latest"
MISTRAL_API_URL = "https://api.mistral.ai/v1/chat/completions"

# URL нашего MCP сервера
MCP_SERVER_URL = "http://localhost:8000"

app = FastAPI(title="Services Chat Assistant")

class ChatMessage(BaseModel):
    message: str

class ChatResponse(BaseModel):
    response: str
    timestamp: str

class MistralClient:
    """Клиент для работы с Mistral AI"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = httpx.AsyncClient()
    
    async def get_services_data(self) -> List[Dict[str, Any]]:
        """Получение данных об услугах из MCP сервера"""
        try:
            response = await self.client.get(f"{MCP_SERVER_URL}/api/services")
            if response.status_code == 200:
                data = response.json()
                return data.get("services", [])
            else:
                logger.error(f"Ошибка получения услуг: {response.status_code}")
                return []
        except Exception as e:
            logger.error(f"Ошибка соединения с MCP сервером: {e}")
            return []
    
    def format_services_for_context(self, services: List[Dict[str, Any]]) -> str:
        """Форматирование услуг для контекста AI"""
        if not services:
            return "База услуг пуста."
        
        context = "ДОСТУПНЫЕ УСЛУГИ:\n\n"
        
        # Группируем по категориям
        categories = {}
        for service in services:
            category = service.get("category", "Общие")
            if category not in categories:
                categories[category] = []
            categories[category].append(service)
        
        for category, category_services in categories.items():
            context += f"📂 {category}:\n"
            for service in category_services:
                status = "✅ Активна" if service.get("active", True) else "❌ Неактивна"
                context += f"  • {service['name']} - {service['price']} ₽ ({status})\n"
                context += f"    Описание: {service['description']}\n"
                context += f"    ID: {service['id']}\n\n"
        
        return context
    
    async def chat_completion(self, messages: List[Dict[str, str]]) -> str:
        """Получение ответа от Mistral AI"""
        try:
            # Получаем актуальные данные об услугах
            services = await self.get_services_data()
            services_context = self.format_services_for_context(services)
            
            # Системный промпт с контекстом услуг
            system_prompt = f"""Ты - консультант по услугам компании. Твоя задача помочь клиентам выбрать подходящие услуги.

{services_context}

ПРАВИЛА:
1. Отвечай только на русском языке
2. Будь дружелюбным и профессиональным
3. Предлагай конкретные услуги из базы данных
4. Указывай точные цены и описания
5. Если услуги нет в базе - честно скажи об этом
6. Можешь рекомендовать несколько услуг для комплексного решения
7. Всегда указывай ID услуги для заказа
8. Если клиент спрашивает про цены - называй точные суммы из базы

Отвечай кратко и по делу. Не придумывай услуги, которых нет в базе."""

            # Подготавливаем сообщения для API
            api_messages = [
                {"role": "system", "content": system_prompt}
            ] + messages
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": MISTRAL_MODEL,
                "messages": api_messages,
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            response = await self.client.post(
                MISTRAL_API_URL,
                headers=headers,
                json=payload,
                timeout=30.0
            )
            
            if response.status_code == 200:
                data = response.json()
                return data["choices"][0]["message"]["content"]
            else:
                logger.error(f"Mistral API error: {response.status_code} - {response.text}")
                return "Извините, произошла ошибка при обработке запроса. Попробуйте позже."
                
        except Exception as e:
            logger.error(f"Ошибка при обращении к Mistral AI: {e}")
            return "Извините, сервис временно недоступен. Попробуйте позже."
    
    async def close(self):
        """Закрытие HTTP клиента"""
        await self.client.aclose()

# Глобальный клиент Mistral
mistral_client = MistralClient(MISTRAL_API_KEY)

# История чата (в реальном приложении лучше использовать базу данных)
chat_history: List[Dict[str, str]] = []

@app.get("/", response_class=HTMLResponse)
async def chat_page():
    """Главная страница чата"""
    html_content = """
    <!DOCTYPE html>
    <html lang="ru">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Консультант по услугам</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            .chat-container {
                height: 70vh;
                overflow-y: auto;
                border: 1px solid #dee2e6;
                border-radius: 0.375rem;
                padding: 1rem;
                background-color: #f8f9fa;
            }
            .message {
                margin-bottom: 1rem;
                padding: 0.75rem;
                border-radius: 0.5rem;
                max-width: 80%;
            }
            .user-message {
                background-color: #007bff;
                color: white;
                margin-left: auto;
                text-align: right;
            }
            .assistant-message {
                background-color: white;
                border: 1px solid #dee2e6;
                margin-right: auto;
            }
            .timestamp {
                font-size: 0.75rem;
                opacity: 0.7;
                margin-top: 0.25rem;
            }
            .typing-indicator {
                display: none;
                font-style: italic;
                color: #6c757d;
            }
        </style>
    </head>
    <body>
        <div class="container mt-4">
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">🤖 Консультант по услугам</h5>
                            <small>Спросите меня о наших услугах!</small>
                        </div>
                        <div class="card-body">
                            <div id="chatContainer" class="chat-container">
                                <div class="message assistant-message">
                                    <div>Привет! Я ваш консультант по услугам. Спросите меня о любой услуге, и я помогу вам выбрать подходящий вариант! 😊</div>
                                    <div class="timestamp">Сейчас</div>
                                </div>
                            </div>
                            <div id="typingIndicator" class="typing-indicator">
                                Консультант печатает...
                            </div>
                            <div class="mt-3">
                                <div class="input-group">
                                    <input type="text" id="messageInput" class="form-control" 
                                           placeholder="Напишите ваш вопрос..." maxlength="500">
                                    <button class="btn btn-primary" id="sendButton" onclick="sendMessage()">
                                        Отправить
                                    </button>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    💡 Примеры вопросов: "Какие у вас IT услуги?", "Сколько стоит создание сайта?", "Нужна консультация по бизнесу"
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script>
            const chatContainer = document.getElementById('chatContainer');
            const messageInput = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const typingIndicator = document.getElementById('typingIndicator');

            function addMessage(content, isUser = false) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
                
                const now = new Date();
                const timestamp = now.toLocaleTimeString('ru-RU', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                });
                
                messageDiv.innerHTML = `
                    <div>${content}</div>
                    <div class="timestamp">${timestamp}</div>
                `;
                
                chatContainer.appendChild(messageDiv);
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            function showTyping() {
                typingIndicator.style.display = 'block';
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }

            function hideTyping() {
                typingIndicator.style.display = 'none';
            }

            async function sendMessage() {
                const message = messageInput.value.trim();
                if (!message) return;

                // Добавляем сообщение пользователя
                addMessage(message, true);
                messageInput.value = '';
                sendButton.disabled = true;
                showTyping();

                try {
                    const response = await fetch('/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ message: message })
                    });

                    const data = await response.json();
                    hideTyping();
                    
                    if (response.ok) {
                        addMessage(data.response);
                    } else {
                        addMessage('Извините, произошла ошибка. Попробуйте позже.');
                    }
                } catch (error) {
                    hideTyping();
                    addMessage('Ошибка соединения. Проверьте интернет-подключение.');
                } finally {
                    sendButton.disabled = false;
                    messageInput.focus();
                }
            }

            // Отправка по Enter
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Функция добавления услуги
            async function addService() {
                const name = document.getElementById('serviceName').value.trim();
                const description = document.getElementById('serviceDescription').value.trim();
                const price = parseFloat(document.getElementById('servicePrice').value);
                const category = document.getElementById('serviceCategory').value;

                if (!name || !description || !price || price <= 0) {
                    alert('Пожалуйста, заполните все поля корректно');
                    return;
                }

                try {
                    const response = await fetch('http://localhost:8000/api/services', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            name: name,
                            description: description,
                            price: price,
                            category: category
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // Закрываем модальное окно
                        const modal = bootstrap.Modal.getInstance(document.getElementById('addServiceModal'));
                        modal.hide();

                        // Очищаем форму
                        document.getElementById('addServiceForm').reset();

                        // Добавляем сообщение в чат
                        addMessage(`✅ Услуга "${name}" успешно добавлена! Теперь я могу консультировать по ней.`, false);

                        // Автоматически спрашиваем об услуге
                        setTimeout(() => {
                            addMessage(`Расскажи мне об услуге "${name}"`, true);
                            sendMessageToAPI(`Расскажи мне об услуге "${name}"`);
                        }, 1000);
                    } else {
                        alert('Ошибка при добавлении услуги: ' + result.message);
                    }
                } catch (error) {
                    console.error('Ошибка:', error);
                    alert('Ошибка при добавлении услуги. Проверьте подключение к серверу.');
                }
            }

            // Фокус на поле ввода
            messageInput.focus();
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(message: ChatMessage):
    """Endpoint для обработки сообщений чата"""
    try:
        # Добавляем сообщение пользователя в историю
        chat_history.append({"role": "user", "content": message.message})
        
        # Ограничиваем историю последними 10 сообщениями
        if len(chat_history) > 10:
            chat_history.pop(0)
        
        # Получаем ответ от Mistral AI
        response = await mistral_client.chat_completion(chat_history)
        
        # Добавляем ответ ассистента в историю
        chat_history.append({"role": "assistant", "content": response})
        
        return ChatResponse(
            response=response,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Ошибка в chat endpoint: {e}")
        return ChatResponse(
            response="Извините, произошла ошибка. Попробуйте позже.",
            timestamp=datetime.now().isoformat()
        )

@app.get("/health")
async def health_check():
    """Проверка здоровья сервиса"""
    return {"status": "ok", "timestamp": datetime.now().isoformat()}

@app.on_event("shutdown")
async def shutdown_event():
    """Закрытие ресурсов при остановке сервера"""
    await mistral_client.close()

if __name__ == "__main__":
    import uvicorn
    print("🚀 Запуск чат-сервера...")
    print("💬 Чат доступен по адресу: http://localhost:8001")
    print("🔗 Убедитесь, что MCP сервер запущен на http://localhost:8000")
    uvicorn.run(app, host="127.0.0.1", port=8001)
