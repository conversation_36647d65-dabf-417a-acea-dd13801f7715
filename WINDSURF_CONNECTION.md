# 🌊 Подключение Windsurf к MCP серверу

## 🎯 Наш сервер работает по SSE на http://localhost:8000

**ВСЕ РАБОТАЕТ ЧЕРЕЗ ОДИН СЕРВЕР:**
- ✅ Веб-интерфейс управления услугами
- ✅ MCP Tools, Resources, Prompts
- ✅ SSE для real-time обновлений
- ✅ Чат-консультант видит те же данные

## 🔗 Подключение Windsurf к нашему SSE серверу:

### 1. Убедитесь, что сервер запущен:
```bash
cd /home/<USER>/My/python-mcp
source venv/bin/activate
python main.py
```

### 2. В Windsurf добавьте MCP сервер:

**Е<PERSON><PERSON>и Windsurf поддерживает SSE/HTTP подключение:**
```
URL: http://localhost:8000
Transport: SSE
```

**Если нужна STDIO конфигурация:**
```json
{
  "mcpServers": {
    "services-manager": {
      "command": "/home/<USER>/My/python-mcp/venv/bin/python",
      "args": ["/home/<USER>/My/python-mcp/main.py"],
      "cwd": "/home/<USER>/My/python-mcp",
      "env": {
        "MCP_TRANSPORT": "stdio"
      }
    }
  }
}
```

## 🛠️ Доступные MCP компоненты для тестирования:

### Tools (Инструменты):
- `add_service` - добавить услугу
- `remove_service` - удалить услугу  
- `list_services` - список услуг
- `update_service` - обновить услугу

### Resources (Ресурсы):
- `services://all` - все услуги
- `services://category/{category}` - услуги по категории
- `services://service/{id}` - конкретная услуга

### Prompts (Промпты):
- `service_consultation` - консультация по услугам
- `service_analysis` - анализ услуг

## 🧪 Тестовые команды для Windsurf:

### 1. Получить список услуг:
```
Используй MCP tool "list_services" чтобы показать все доступные услуги
```

### 2. Добавить новую услугу:
```
Используй MCP tool "add_service" чтобы добавить услугу:
- name: "Тестовая услуга от Windsurf"
- description: "Услуга добавленная через Windsurf MCP"
- price: 2500.0
- category: "Тестирование"
```

### 3. Получить ресурс:
```
Используй MCP resource "services://all" чтобы получить данные всех услуг
```

### 4. Использовать промпт:
```
Используй MCP prompt "service_consultation" с параметром:
user_request: "Нужна помощь с созданием мобильного приложения"
```

## � ВАЖНО: Real-time синхронизация!

**Все изменения синхронизируются между:**
- ✅ Веб-интерфейсом (http://localhost:8000)
- ✅ Windsurf MCP клиентом
- ✅ AI чат-консультантом (http://localhost:8001)
- ✅ Любыми другими MCP клиентами

**Тестируйте так:**
1. Добавьте услугу через Windsurf MCP
2. Обновите веб-страницу - увидите новую услугу
3. Спросите чат-консультанта - он знает о новой услуге
4. Все работает в real-time!

## �🔍 Проверка подключения:

### В Windsurf должны появиться:
- ✅ MCP сервер "services-manager" в списке подключенных
- ✅ 4 доступных инструмента (tools)
- ✅ 3 доступных ресурса (resources)  
- ✅ 2 доступных промпта (prompts)

### Логи сервера покажут:
```
INFO: MCP client connected
INFO: Tools requested: ['add_service', 'remove_service', 'list_services', 'update_service']
INFO: Resources requested: ['services://all', 'services://category/*', 'services://service/*']
INFO: Prompts requested: ['service_consultation', 'service_analysis']
```

## 🎯 Что можно тестировать:

### 1. **CRUD операции с услугами:**
- Добавление новых услуг через MCP
- Просмотр существующих услуг
- Обновление данных услуг
- Удаление услуг

### 2. **Работа с ресурсами:**
- Получение всех услуг в JSON формате
- Фильтрация по категориям
- Получение конкретной услуги по ID

### 3. **AI промпты:**
- Генерация консультационных промптов
- Анализ базы услуг
- Создание рекомендаций

### 4. **Интеграция с веб-интерфейсом:**
- Изменения через MCP отражаются в веб-интерфейсе
- Real-time синхронизация через SSE
- Консистентность данных

## 🚀 Преимущества нашего MCP сервера:

- ✅ **Полная совместимость** с MCP протоколом
- ✅ **Real-time обновления** через SSE
- ✅ **Валидация данных** через Pydantic
- ✅ **Веб-интерфейс** для визуального контроля
- ✅ **AI интеграция** с чат-консультантом
- ✅ **Production-ready** код с обработкой ошибок

## 🎉 Готово к тестированию!

Теперь вы можете полноценно тестировать MCP функциональность в Windsurf, используя наш сервер как источник данных об услугах.
