# 🌊 Подключение Windsurf к MCP серверу

## 🎯 У нас работают ОБА транспорта!

- **SSE транспорт** на **http://localhost:8000** - для веб-интерфейса
- **STDIO транспорт** через **mcp_stdio.py** - для MCP клиентов типа Windsurf

## 🔗 Как подключить Windsurf:

### 1. Убедитесь, что серверы запущены:
```bash
# Запуск всей системы
cd /home/<USER>/My/python-mcp
source venv/bin/activate
python start_all.py

# Или только MCP сервер для Windsurf
python main.py
```

### 2. В Windsurf откройте настройки MCP:
- Откройте Command Palette (Ctrl+Shift+P)
- Найдите "MCP: Configure Servers"
- Или откройте файл настроек MCP

### 3. Добавьте конфигурацию нашего сервера:
```json
{
  "mcpServers": {
    "services-manager": {
      "command": "python",
      "args": ["/home/<USER>/My/python-mcp/mcp_stdio.py"],
      "cwd": "/home/<USER>/My/python-mcp",
      "env": {
        "PYTHONPATH": "/home/<USER>/My/python-mcp"
      }
    }
  }
}
```

### Альтернативная конфигурация с виртуальным окружением:
```json
{
  "mcpServers": {
    "services-manager": {
      "command": "/home/<USER>/My/python-mcp/venv/bin/python",
      "args": ["/home/<USER>/My/python-mcp/mcp_stdio.py"],
      "cwd": "/home/<USER>/My/python-mcp"
    }
  }
}
```

### 4. Альтернативно - подключение по HTTP:
Если Windsurf поддерживает HTTP подключение к MCP серверам:
```
URL: http://localhost:8000
Transport: SSE
```

## 🛠️ Доступные MCP компоненты для тестирования:

### Tools (Инструменты):
- `add_service` - добавить услугу
- `remove_service` - удалить услугу  
- `list_services` - список услуг
- `update_service` - обновить услугу

### Resources (Ресурсы):
- `services://all` - все услуги
- `services://category/{category}` - услуги по категории
- `services://service/{id}` - конкретная услуга

### Prompts (Промпты):
- `service_consultation` - консультация по услугам
- `service_analysis` - анализ услуг

## 🧪 Тестовые команды для Windsurf:

### 1. Получить список услуг:
```
Используй MCP tool "list_services" чтобы показать все доступные услуги
```

### 2. Добавить новую услугу:
```
Используй MCP tool "add_service" чтобы добавить услугу:
- name: "Тестовая услуга от Windsurf"
- description: "Услуга добавленная через Windsurf MCP"
- price: 2500.0
- category: "Тестирование"
```

### 3. Получить ресурс:
```
Используй MCP resource "services://all" чтобы получить данные всех услуг
```

### 4. Использовать промпт:
```
Используй MCP prompt "service_consultation" с параметром:
user_request: "Нужна помощь с созданием мобильного приложения"
```

## 🔍 Проверка подключения:

### В Windsurf должны появиться:
- ✅ MCP сервер "services-manager" в списке подключенных
- ✅ 4 доступных инструмента (tools)
- ✅ 3 доступных ресурса (resources)  
- ✅ 2 доступных промпта (prompts)

### Логи сервера покажут:
```
INFO: MCP client connected
INFO: Tools requested: ['add_service', 'remove_service', 'list_services', 'update_service']
INFO: Resources requested: ['services://all', 'services://category/*', 'services://service/*']
INFO: Prompts requested: ['service_consultation', 'service_analysis']
```

## 🎯 Что можно тестировать:

### 1. **CRUD операции с услугами:**
- Добавление новых услуг через MCP
- Просмотр существующих услуг
- Обновление данных услуг
- Удаление услуг

### 2. **Работа с ресурсами:**
- Получение всех услуг в JSON формате
- Фильтрация по категориям
- Получение конкретной услуги по ID

### 3. **AI промпты:**
- Генерация консультационных промптов
- Анализ базы услуг
- Создание рекомендаций

### 4. **Интеграция с веб-интерфейсом:**
- Изменения через MCP отражаются в веб-интерфейсе
- Real-time синхронизация через SSE
- Консистентность данных

## 🚀 Преимущества нашего MCP сервера:

- ✅ **Полная совместимость** с MCP протоколом
- ✅ **Real-time обновления** через SSE
- ✅ **Валидация данных** через Pydantic
- ✅ **Веб-интерфейс** для визуального контроля
- ✅ **AI интеграция** с чат-консультантом
- ✅ **Production-ready** код с обработкой ошибок

## 🎉 Готово к тестированию!

Теперь вы можете полноценно тестировать MCP функциональность в Windsurf, используя наш сервер как источник данных об услугах.
