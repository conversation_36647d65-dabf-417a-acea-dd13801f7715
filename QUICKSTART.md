# 🚀 Быстрый старт - FastMCP Services Manager

## Что это?

Современное веб-приложение для управления услугами, построенное на:
- **MCP Python SDK** - Model Context Protocol для AI интеграций
- **FastAPI** - высокопроизводительный веб-фреймворк
- **Bootstrap 5** - современный UI фреймворк
- **Server-Sent Events (SSE)** - real-time обновления

## ⚡ Быстрый запуск

### 1. Активация окружения
```bash
source venv/bin/activate
```

### 2. Запуск сервера
```bash
python main.py
```

### 3. Открытие в браузере
Перейдите на: **http://localhost:8000**

## 🎯 Что вы увидите

### Веб-интерфейс
- 📝 **Форма добавления услуг** - название, описание, цена, категория
- 📊 **Список услуг** - красивые карточки с информацией
- ⚡ **Real-time обновления** - изменения видны мгновенно
- 🎨 **Адаптивный дизайн** - работает на всех устройствах

### Функции
- ➕ Добавление новых услуг
- 🗑️ Удаление услуг
- 👁️ Просмотр всех услуг
- 🏷️ Категоризация услуг
- 💰 Управление ценами

## 🛠️ Тестирование

### Добавление тестовых данных
```bash
# Добавить демонстрационные услуги
python demo_services.py
```

### Тестирование MCP tools
```bash
# Протестировать MCP инструменты
python test_mcp_tools.py
```

### API тестирование
```bash
# Получить список услуг
curl http://localhost:8000/api/services

# Добавить услугу
curl -X POST http://localhost:8000/api/services \
  -H "Content-Type: application/json" \
  -d '{"name": "Новая услуга", "description": "Описание", "price": 1000.0}'

# Удалить услугу (ID=1)
curl -X DELETE http://localhost:8000/api/services/1
```

## 🔧 Транспорты MCP

### SSE (по умолчанию)
```bash
python main.py
# Веб-интерфейс: http://localhost:8000
```

### STDIO (для MCP клиентов)
```bash
python -c "from main import mcp; mcp.run(transport='stdio')"
```

### Streamable HTTP
```bash
python -c "from main import mcp; mcp.run(transport='streamable-http')"
# API: http://localhost:3000
```

## 📁 Структура файлов

```
├── main.py              # 🎯 Основной сервер
├── static/app.js        # 💻 JavaScript клиент
├── services/manager.py  # 📊 Менеджер услуг
├── services_data.json   # 💾 Данные (создается автоматически)
├── demo_services.py     # 🎭 Демо данные
└── test_mcp_tools.py    # 🧪 Тесты
```

## 🎨 Категории услуг

- **IT услуги** - разработка, настройка серверов
- **Дизайн** - веб-дизайн, логотипы, интерьеры
- **Маркетинг** - SEO, контент-маркетинг
- **Консультации** - бизнес-консультации
- **Общие** - прочие услуги

## 🔍 Особенности

### MCP Tools
- `add_service` - добавление услуги
- `remove_service` - удаление услуги
- `list_services` - список услуг
- `update_service` - обновление услуги

### API Endpoints
- `GET /api/services` - получить услуги
- `POST /api/services` - добавить услугу
- `DELETE /api/services/{id}` - удалить услугу
- `GET /sse` - Server-Sent Events

### Real-time функции
- ⚡ Мгновенные обновления через SSE
- 🔄 Автоматическая синхронизация
- 💓 Heartbeat соединения

## 🚨 Troubleshooting

### Порт занят?
```bash
# Проверить процессы на порту 8000
lsof -i :8000

# Убить процесс
kill -9 <PID>
```

### Проблемы с зависимостями?
```bash
# Переустановить зависимости
pip install -r requirements.txt
```

### Данные не сохраняются?
```bash
# Проверить права на файл
ls -la services_data.json

# Пересоздать файл
rm services_data.json
python main.py
```

## 🎉 Готово!

Теперь у вас есть полнофункциональное веб-приложение для управления услугами с:
- ✅ Современным веб-интерфейсом
- ✅ MCP интеграцией
- ✅ Real-time обновлениями
- ✅ REST API
- ✅ Адаптивным дизайном

**Наслаждайтесь использованием! 🚀**
