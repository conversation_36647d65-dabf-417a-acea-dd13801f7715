#!/usr/bin/env python3
"""
FastMCP Services Manager Server
Сервер для управления услугами с веб-интерфейсом и SSE поддержкой
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

from mcp.server.fastmcp import FastMCP, Context
from pydantic import Field
from starlette.requests import Request
from starlette.responses import HTMLResponse, JSONResponse, Response
from starlette.staticfiles import StaticFiles
from sse_starlette import EventSourceResponse
import asyncio

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Создание FastMCP сервера
mcp = FastMCP(
    name="Services Manager",
    instructions="Сервер для управления услугами с веб-интерфейсом"
)

# Путь к файлу с данными услуг
SERVICES_FILE = Path("services_data.json")

# Инициализация данных
def init_services_data():
    """Инициализация файла с данными услуг"""
    if not SERVICES_FILE.exists():
        initial_data = {
            "services": [],
            "last_updated": datetime.now().isoformat()
        }
        with open(SERVICES_FILE, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, ensure_ascii=False, indent=2)

def load_services() -> List[Dict[str, Any]]:
    """Загрузка списка услуг"""
    init_services_data()
    try:
        with open(SERVICES_FILE, 'r', encoding='utf-8') as f:
            data = json.load(f)
            return data.get("services", [])
    except Exception as e:
        logger.error(f"Ошибка загрузки услуг: {e}")
        return []

def save_services(services: List[Dict[str, Any]]):
    """Сохранение списка услуг"""
    try:
        data = {
            "services": services,
            "last_updated": datetime.now().isoformat()
        }
        with open(SERVICES_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"Ошибка сохранения услуг: {e}")

# MCP Tools для управления услугами
@mcp.tool()
def add_service(
    name: str = Field(description="Название услуги", min_length=1, max_length=100),
    description: str = Field(description="Описание услуги", min_length=1, max_length=500),
    price: float = Field(description="Цена услуги в рублях", gt=0),
    category: str = Field(description="Категория услуги", default="Общие"),
    ctx: Context = None
) -> str:
    """Добавить новую услугу в базу данных"""
    if ctx:
        ctx.info(f"Добавление услуги: {name}")

    services = load_services()

    # Проверка на дублирование
    for service in services:
        if service["name"].lower() == name.lower():
            if ctx:
                ctx.warning(f"Попытка добавить дублирующую услугу: {name}")
            return f"Услуга '{name}' уже существует"

    new_service = {
        "id": len(services) + 1,
        "name": name,
        "description": description,
        "price": price,
        "category": category,
        "created_at": datetime.now().isoformat(),
        "active": True
    }

    services.append(new_service)
    save_services(services)

    if ctx:
        ctx.info(f"Услуга '{name}' успешно добавлена с ID {new_service['id']}")

    return f"Услуга '{name}' успешно добавлена"

@mcp.tool()
def remove_service(
    service_id: int = Field(description="ID услуги для удаления", gt=0),
    ctx: Context = None
) -> str:
    """Удалить услугу по ID из базы данных"""
    if ctx:
        ctx.info(f"Удаление услуги с ID: {service_id}")

    services = load_services()

    for i, service in enumerate(services):
        if service["id"] == service_id:
            removed_service = services.pop(i)
            save_services(services)
            if ctx:
                ctx.info(f"Услуга '{removed_service['name']}' успешно удалена")
            return f"Услуга '{removed_service['name']}' удалена"

    if ctx:
        ctx.warning(f"Услуга с ID {service_id} не найдена")
    return f"Услуга с ID {service_id} не найдена"

@mcp.tool()
def list_services() -> str:
    """Получить список всех услуг"""
    services = load_services()
    
    if not services:
        return "Список услуг пуст"
    
    result = "Список услуг:\n"
    for service in services:
        status = "Активна" if service.get("active", True) else "Неактивна"
        result += f"ID: {service['id']}, Название: {service['name']}, "
        result += f"Цена: {service['price']} руб., Категория: {service['category']}, "
        result += f"Статус: {status}\n"
    
    return result

@mcp.tool()
def update_service(service_id: int, name: str = None, description: str = None, 
                  price: float = None, category: str = None, active: bool = None) -> str:
    """Обновить данные услуги"""
    services = load_services()
    
    for service in services:
        if service["id"] == service_id:
            if name is not None:
                service["name"] = name
            if description is not None:
                service["description"] = description
            if price is not None:
                service["price"] = price
            if category is not None:
                service["category"] = category
            if active is not None:
                service["active"] = active
            
            service["updated_at"] = datetime.now().isoformat()
            save_services(services)
            return f"Услуга с ID {service_id} обновлена"
    
    return f"Услуга с ID {service_id} не найдена"

# MCP Resources для доступа к данным
@mcp.resource("services://all")
def services_resource() -> str:
    """Получить все услуги в формате JSON"""
    services = load_services()
    return json.dumps(services, ensure_ascii=False, indent=2)

@mcp.resource("services://category/{category}")
def services_by_category_resource(category: str) -> str:
    """Получить услуги по категории"""
    services = load_services()
    filtered_services = [s for s in services if s.get("category", "").lower() == category.lower()]
    return json.dumps(filtered_services, ensure_ascii=False, indent=2)

@mcp.resource("services://service/{service_id}")
def service_by_id_resource(service_id: str) -> str:
    """Получить конкретную услугу по ID"""
    try:
        sid = int(service_id)
        services = load_services()
        for service in services:
            if service["id"] == sid:
                return json.dumps(service, ensure_ascii=False, indent=2)
        return json.dumps({"error": f"Услуга с ID {sid} не найдена"}, ensure_ascii=False)
    except ValueError:
        return json.dumps({"error": "Неверный формат ID"}, ensure_ascii=False)

# MCP Prompts для AI взаимодействий
@mcp.prompt("service_consultation")
def service_consultation_prompt(
    user_request: str = Field(description="Запрос пользователя о услугах")
) -> str:
    """Создать промпт для консультации по услугам"""
    services = load_services()
    services_info = "\n".join([
        f"- {s['name']}: {s['description']} (Цена: {s['price']} ₽, Категория: {s['category']})"
        for s in services if s.get('active', True)
    ])

    return f"""Ты консультант по услугам компании. Пользователь спрашивает: "{user_request}"

Доступные услуги:
{services_info}

Дай профессиональную консультацию, предложи подходящие услуги с указанием цен и ID для заказа."""

@mcp.prompt("service_analysis")
def service_analysis_prompt() -> str:
    """Создать промпт для анализа услуг"""
    services = load_services()
    categories = {}
    total_value = 0

    for service in services:
        category = service.get("category", "Общие")
        if category not in categories:
            categories[category] = []
        categories[category].append(service)
        total_value += service.get("price", 0)

    analysis = f"Анализ базы услуг:\n"
    analysis += f"Всего услуг: {len(services)}\n"
    analysis += f"Общая стоимость: {total_value} ₽\n"
    analysis += f"Категории: {', '.join(categories.keys())}\n\n"

    for category, cat_services in categories.items():
        analysis += f"{category}: {len(cat_services)} услуг\n"

    return f"""Проанализируй следующую информацию об услугах компании:

{analysis}

Дай рекомендации по оптимизации ценообразования и расширению линейки услуг."""

# Веб-интерфейс routes
@mcp.custom_route("/", methods=["GET"])
async def index(request: Request) -> HTMLResponse:
    """Главная страница"""
    html_content = """
    <!DOCTYPE html>
    <html lang="ru">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Управление услугами</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <style>
            .service-card {
                transition: transform 0.2s;
            }
            .service-card:hover {
                transform: translateY(-2px);
            }
            .price {
                font-weight: bold;
                color: #28a745;
            }
            .status-active {
                color: #28a745;
            }
            .status-inactive {
                color: #dc3545;
            }
        </style>
    </head>
    <body>
        <div class="container mt-4">
            <h1 class="mb-4">Управление услугами</h1>
            
            <!-- Форма добавления услуги -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5>Добавить новую услугу</h5>
                </div>
                <div class="card-body">
                    <form id="addServiceForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="serviceName" class="form-label">Название услуги</label>
                                    <input type="text" class="form-control" id="serviceName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="servicePrice" class="form-label">Цена (руб.)</label>
                                    <input type="number" class="form-control" id="servicePrice" step="0.01" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="serviceCategory" class="form-label">Категория</label>
                                    <select class="form-control" id="serviceCategory">
                                        <option value="Общие">Общие</option>
                                        <option value="IT услуги">IT услуги</option>
                                        <option value="Консультации">Консультации</option>
                                        <option value="Дизайн">Дизайн</option>
                                        <option value="Маркетинг">Маркетинг</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="serviceDescription" class="form-label">Описание</label>
                            <textarea class="form-control" id="serviceDescription" rows="3" required></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Добавить услугу</button>
                    </form>
                </div>
            </div>
            
            <!-- Список услуг -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5>Список услуг</h5>
                    <button class="btn btn-outline-primary btn-sm" onclick="loadServices()">Обновить</button>
                </div>
                <div class="card-body">
                    <div id="servicesList" class="row">
                        <!-- Услуги будут загружены здесь -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Модальное окно для уведомлений -->
        <div class="modal fade" id="notificationModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Уведомление</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body" id="notificationBody">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Закрыть</button>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script src="/static/app.js"></script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

# API endpoints
@mcp.custom_route("/api/services", methods=["GET"])
async def get_services_api(request: Request) -> JSONResponse:
    """API для получения списка услуг"""
    services = load_services()
    return JSONResponse({"services": services})

@mcp.custom_route("/api/services", methods=["POST"])
async def add_service_api(request: Request) -> JSONResponse:
    """API для добавления услуги"""
    try:
        data = await request.json()
        result = add_service(
            name=data["name"],
            description=data["description"],
            price=float(data["price"]),
            category=data.get("category", "Общие")
        )
        return JSONResponse({"success": True, "message": result})
    except Exception as e:
        return JSONResponse({"success": False, "message": str(e)}, status_code=400)

@mcp.custom_route("/api/services/{service_id}", methods=["DELETE"])
async def delete_service_api(request: Request) -> JSONResponse:
    """API для удаления услуги"""
    try:
        service_id = int(request.path_params["service_id"])
        result = remove_service(service_id)
        return JSONResponse({"success": True, "message": result})
    except Exception as e:
        return JSONResponse({"success": False, "message": str(e)}, status_code=400)

# SSE endpoint для real-time обновлений
@mcp.custom_route("/sse", methods=["GET"])
async def sse_endpoint(request: Request) -> EventSourceResponse:
    """SSE endpoint для отправки обновлений клиентам"""
    async def event_generator():
        try:
            # Отправляем приветственное сообщение
            yield {
                "event": "connected",
                "data": json.dumps({"message": "Соединение установлено", "timestamp": datetime.now().isoformat()})
            }

            # Периодически отправляем heartbeat
            while True:
                await asyncio.sleep(30)  # Heartbeat каждые 30 секунд
                yield {
                    "event": "heartbeat",
                    "data": json.dumps({"timestamp": datetime.now().isoformat()})
                }
        except asyncio.CancelledError:
            logger.info("SSE соединение закрыто")
            raise
        except Exception as e:
            logger.error(f"Ошибка в SSE: {e}")
            yield {
                "event": "error",
                "data": json.dumps({"error": str(e)})
            }

    return EventSourceResponse(event_generator())

# Настройка статических файлов
@mcp.custom_route("/static/{path:path}", methods=["GET"])
async def static_files(request: Request) -> Response:
    """Обслуживание статических файлов"""
    file_path = request.path_params["path"]
    static_file_path = Path("static") / file_path

    if not static_file_path.exists():
        return JSONResponse({"error": "File not found"}, status_code=404)

    # Определение MIME типа
    content_type = "text/plain"
    if file_path.endswith(".js"):
        content_type = "application/javascript"
    elif file_path.endswith(".css"):
        content_type = "text/css"
    elif file_path.endswith(".html"):
        content_type = "text/html"

    with open(static_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    return Response(content=content, media_type=content_type)

if __name__ == "__main__":
    # Инициализация данных
    init_services_data()
    
    # Запуск сервера с SSE транспортом
    print("Запуск сервера на http://localhost:8000")
    print("Веб-интерфейс доступен по адресу: http://localhost:8000")
    mcp.run(transport="sse")
