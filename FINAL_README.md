# 🎯 Система управления услугами с AI-консультантом

Полнофункциональная система на основе MCP Python SDK с веб-интерфейсом управления услугами и AI-консультантом на Mistral AI.

## 🌟 Что создано

### 1. **MCP Сервер услуг** (`main.py`)
- ✅ FastMCP сервер с полной интеграцией MCP SDK
- ✅ SSE транспорт для real-time обновлений  
- ✅ REST API для веб-интерфейса
- ✅ MCP tools для управления услугами
- ✅ Веб-интерфейс на Bootstrap 5

### 2. **AI Чат-консультант** (`chat_server.py`)
- ✅ Интеграция с Mistral AI (mistral-small-latest)
- ✅ Минималистичный чат-интерфейс
- ✅ Консультации по всей базе услуг
- ✅ Контекстные ответы на русском языке
- ✅ Real-time общение

### 3. **Система управления данными**
- ✅ JSON-based хранение услуг
- ✅ Автоматическое создание файлов
- ✅ Валидация и обработка ошибок
- ✅ Менеджер услуг с полным CRUD

## 🚀 Быстрый старт

### Автоматический запуск всей системы
```bash
# 1. Активация окружения
source venv/bin/activate

# 2. Запуск обеих серверов
python start_all.py
```

### Ручной запуск (альтернатива)
```bash
# Терминал 1: MCP сервер
source venv/bin/activate
python main.py

# Терминал 2: Чат-сервер  
source venv/bin/activate
python chat_server.py
```

## 🌐 Доступные интерфейсы

| Сервис | URL | Описание |
|--------|-----|----------|
| 📊 **Управление услугами** | http://localhost:8000 | Веб-интерфейс для CRUD операций |
| 💬 **AI Консультант** | http://localhost:8001 | Чат с Mistral AI по услугам |

## 🎯 Функциональность

### MCP Tools (для AI интеграций)
- `add_service` - добавление услуги
- `remove_service` - удаление услуги
- `list_services` - список всех услуг
- `update_service` - обновление данных

### REST API
- `GET /api/services` - получить услуги
- `POST /api/services` - добавить услугу
- `DELETE /api/services/{id}` - удалить услугу
- `GET /sse` - Server-Sent Events

### AI Консультант
- 🔍 Поиск услуг по категориям
- 💰 Информация о ценах
- 📝 Детальные описания
- 🎯 Персональные рекомендации
- 📦 Комплексные решения

## 📁 Структура проекта

```
python-mcp/
├── 🎯 Основные серверы
│   ├── main.py              # MCP сервер услуг
│   └── chat_server.py       # AI чат-консультант
│
├── 🌐 Веб-интерфейсы  
│   └── static/
│       └── app.js           # JavaScript для управления услугами
│
├── 📊 Управление данными
│   ├── services/
│   │   ├── __init__.py
│   │   └── manager.py       # Менеджер услуг
│   └── services_data.json   # База данных (создается автоматически)
│
├── 🛠️ Утилиты и тесты
│   ├── start_all.py         # Запуск всей системы
│   ├── demo_services.py     # Демонстрационные данные
│   └── test_mcp_tools.py    # Тестирование MCP tools
│
└── 📚 Документация
    ├── QUICKSTART.md        # Быстрый старт
    ├── CHAT_README.md       # Документация чата
    ├── README_PROJECT.md    # Подробное описание
    └── requirements.txt     # Зависимости
```

## 🎭 Демонстрация

### Добавление тестовых данных
```bash
# Добавить 8 демонстрационных услуг
python demo_services.py
```

### Тестирование API
```bash
# Получить список услуг
curl http://localhost:8000/api/services

# Добавить услугу
curl -X POST http://localhost:8000/api/services \
  -H "Content-Type: application/json" \
  -d '{"name": "Новая услуга", "description": "Описание", "price": 1000.0}'

# Тест чата
curl -X POST http://localhost:8001/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Какие у вас IT услуги?"}'
```

## 🤖 Примеры работы AI

### Консультация по услугам
```
👤 Пользователь: Нужна помощь с созданием сайта
🤖 Консультант: Отлично! У нас есть несколько вариантов для создания сайта:

1. **Разработка веб-сайта** - 50,000 ₽ (ID: 3)
   Создание современного адаптивного веб-сайта с использованием последних технологий

2. **Веб-разработка** - 45,000 ₽ (ID: 11)  
   Создание современных веб-приложений

Также рекомендую дополнительно:
- **Веб-дизайн** - 4,500 ₽ (ID: 3) для красивого оформления
- **SEO оптимизация** - 15,000 ₽ (ID: 4) для продвижения

Какой вариант вас больше интересует?
```

### Поиск по категориям
```
👤 Пользователь: Покажи все дизайнерские услуги
🤖 Консультант: Вот наши дизайнерские услуги:

📂 Дизайн:
• Веб-дизайн - 4,500 ₽ (ID: 3)
  Создание современных адаптивных веб-сайтов
  
• Логотип и фирменный стиль - 25,000 ₽ (ID: 5)
  Разработка уникального логотипа и создание фирменного стиля компании
  
• Дизайн интерьера - 35,000 ₽ (ID: 10)
  Создание дизайн-проекта интерьера с 3D визуализацией

Все услуги активны и доступны для заказа!
```

## 🔧 Технические особенности

### MCP SDK интеграция
- Полное соответствие MCP протоколу
- Поддержка всех транспортов (stdio, sse, streamable-http)
- Правильная обработка tools и resources
- Контекстное управление сессиями

### AI интеграция
- Mistral AI API с моделью mistral-small-latest
- Контекстные промпты с актуальными данными
- Обработка ошибок и fallback ответы
- История диалогов

### Веб-технологии
- FastAPI для высокой производительности
- Bootstrap 5 для современного UI
- Server-Sent Events для real-time
- Адаптивный дизайн

## 🛡️ Безопасность

- ✅ Валидация входных данных
- ✅ Обработка ошибок API
- ✅ Ограничение размера сообщений
- ✅ Таймауты для внешних запросов

## 📈 Масштабирование

### Для продакшена рекомендуется:
- Замена JSON файлов на PostgreSQL/MongoDB
- Добавление аутентификации и авторизации
- Кэширование ответов AI
- Логирование и мониторинг
- Docker контейнеризация

## 🎉 Результат

Создана полнофункциональная система, которая:

✅ **Соответствует всем требованиям MCP SDK**
✅ **Имеет современный веб-интерфейс на Bootstrap**  
✅ **Интегрирована с Mistral AI для консультаций**
✅ **Поддерживает real-time обновления через SSE**
✅ **Работает как MCP сервер для AI интеграций**
✅ **Предоставляет REST API для внешних приложений**
✅ **Готова к использованию и дальнейшему развитию**

**Система полностью готова к работе! 🚀**

---

*Создано с использованием MCP Python SDK, FastAPI, Bootstrap 5 и Mistral AI*
